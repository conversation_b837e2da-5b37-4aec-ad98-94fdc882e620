---
title: Setup
icon: TbServer
image: /images/user-guide/table-views/table.png
---

import OptionTable from '@site/src/theme/OptionTable'

# Configuration Management

<ArticleWarning>
**First time installing?** Follow the [Docker Compose installation guide](https://twenty.com/developers/section/self-hosting/docker-compose) to get Twenty running, then return here for configuration.
</ArticleWarning>

Twenty offers **two configuration modes** to suit different deployment needs:

**Admin panel access:** Only users with admin privileges (`canAccessFullAdminPanel: true`) can access the configuration interface.

## 1. Admin Panel Configuration (Default)

```bash
IS_CONFIG_VARIABLES_IN_DB_ENABLED=true  # default
```

**Most configuration happens through the UI** after installation:

1. Access your Twenty instance (usually `http://localhost:3000`)
2. Go to **Settings / Admin Panel / Configuration Variables**  
3. Configure integrations, email, storage, and more
4. Changes take effect immediately (within 15 seconds for multi-container deployments)

<ArticleWarning>
**Multi-Container Deployments:** When using database configuration (`IS_CONFIG_VARIABLES_IN_DB_ENABLED=true`), both server and worker containers read from the same database. Admin panel changes affect both automatically, eliminating the need to duplicate environment variables between containers (except for infrastructure variables).
</ArticleWarning>

**What you can configure through the admin panel:**
- **Authentication** - Google/Microsoft OAuth, password settings
- **Email** - SMTP settings, templates, verification  
- **Storage** - S3 configuration, local storage paths
- **Integrations** - Gmail, Google Calendar, Microsoft services
- **Workflow & Rate Limiting** - Execution limits, API throttling
- **And much more...**

![Admin Panel Configuration Variables](/images/user-guide/setup/admin-panel-config-variables.png)

<ArticleWarning>
**Some configuration requires setting in .env:** Infrastructure like database connections (`PG_DATABASE_URL`), server URLs (`SERVER_URL`), and app secrets (`APP_SECRET`) can only be configured via `.env` file.
Those configurations are marked as `isEnvOnly: true` in the source code.

[See complete list of environment-only variables →](https://github.com/twentyhq/twenty/blob/main/packages/twenty-server/src/engine/core-modules/twenty-config/config-variables.ts)
</ArticleWarning>

## 2. Environment-Only Configuration

```bash
IS_CONFIG_VARIABLES_IN_DB_ENABLED=false
```

**All configuration managed through `.env` files:**

1. Set `IS_CONFIG_VARIABLES_IN_DB_ENABLED=false` in your `.env` file
2. Add all configuration variables to your `.env` file
3. Restart containers for changes to take effect
4. Admin panel will show current values but cannot modify them

## Gmail & Google Calendar Integration

### Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable these APIs:
- [Gmail API](https://console.cloud.google.com/apis/library/gmail.googleapis.com)
- [Google Calendar API](https://console.cloud.google.com/apis/library/calendar-json.googleapis.com)
- [People API](https://console.cloud.google.com/apis/library/people.googleapis.com)

### Configure OAuth

1. Go to [Credentials](https://console.cloud.google.com/apis/credentials)
2. Create OAuth 2.0 Client ID
3. Add these redirect URIs:
   - `https://<your-domain>/auth/google/redirect` (for SSO)
   - `https://<your-domain>/auth/google-apis/get-access-token` (for integrations)

### Configure in Twenty

1. Go to **Settings → Admin Panel → Configuration Variables**
2. Find the **Google Auth** section
3. Set these variables:
   - `MESSAGING_PROVIDER_GMAIL_ENABLED=true`
   - `CALENDAR_PROVIDER_GOOGLE_ENABLED=true`
   - `AUTH_GOOGLE_CLIENT_ID=<client-id>`
   - `AUTH_GOOGLE_CLIENT_SECRET=<client-secret>`
   - `AUTH_GOOGLE_CALLBACK_URL=https://<your-domain>/auth/google/redirect`
   - `AUTH_GOOGLE_APIS_CALLBACK_URL=https://<your-domain>/auth/google-apis/get-access-token`

<ArticleWarning>
**Environment-only mode:** If you set `IS_CONFIG_VARIABLES_IN_DB_ENABLED=false`, add these variables to your `.env` file instead.
</ArticleWarning>

**Required scopes** (automatically configured):
[See relevant source code](https://github.com/twentyhq/twenty/blob/main/packages/twenty-server/src/engine/core-modules/auth/utils/get-google-apis-oauth-scopes.ts#L4-L10)
- `https://www.googleapis.com/auth/calendar.events`
- `https://www.googleapis.com/auth/gmail.readonly`  
- `https://www.googleapis.com/auth/profile.emails.read`

### If your app is in test mode

If your app is in test mode, you will need to add test users to your project.

Under [OAuth consent screen](https://console.cloud.google.com/apis/credentials/consent), add your test users to the "Test users" section.

## Microsoft 365 Integration

<ArticleWarning>
Users must have a [Microsoft 365 Licence](https://admin.microsoft.com/Adminportal/Home) to be able to use the Calendar and Messaging API. They will not be able to sync their account on Twenty without one.
</ArticleWarning>

### Create a project in Microsoft Azure

You will need to create a project in [Microsoft Azure](https://portal.azure.com/#view/Microsoft_AAD_IAM/AppGalleryBladeV2) and get the credentials.

### Enable APIs

On Microsoft Azure Console enable the following APIs in "Permissions":

- Microsoft Graph: Mail.ReadWrite
- Microsoft Graph: Mail.Send
- Microsoft Graph: Calendars.Read
- Microsoft Graph: User.Read
- Microsoft Graph: openid
- Microsoft Graph: email
- Microsoft Graph: profile
- Microsoft Graph: offline_access

Note: "Mail.ReadWrite" and "Mail.Send" are only mandatory if you want to send emails using our workflow actions. You can use "Mail.Read" instead if you only want to receive emails.

### Authorized redirect URIs

You need to add the following redirect URIs to your project:
- `https://<your-domain>/auth/microsoft/redirect` if you want to use Microsoft SSO
- `https://<your-domain>/auth/microsoft-apis/get-access-token`

### Configure in Twenty

1. Go to **Settings → Admin Panel → Configuration Variables**
2. Find the **Microsoft Auth** section
3. Set these variables:
   - `MESSAGING_PROVIDER_MICROSOFT_ENABLED=true`
   - `CALENDAR_PROVIDER_MICROSOFT_ENABLED=true`
   - `AUTH_MICROSOFT_ENABLED=true`
   - `AUTH_MICROSOFT_CLIENT_ID=<client-id>`
   - `AUTH_MICROSOFT_CLIENT_SECRET=<client-secret>`
   - `AUTH_MICROSOFT_CALLBACK_URL=https://<your-domain>/auth/microsoft/redirect`
   - `AUTH_MICROSOFT_APIS_CALLBACK_URL=https://<your-domain>/auth/microsoft-apis/get-access-token`

<ArticleWarning>
**Environment-only mode:** If you set `IS_CONFIG_VARIABLES_IN_DB_ENABLED=false`, add these variables to your `.env` file instead.
</ArticleWarning>

### Configure scopes
[See relevant source code](https://github.com/twentyhq/twenty/blob/main/packages/twenty-server/src/engine/core-modules/auth/utils/get-microsoft-apis-oauth-scopes.ts#L2-L9)
- 'openid'
- 'email'
- 'profile'
- 'offline_access'
- 'Mail.ReadWrite'
- 'Mail.Send'
- 'Calendars.Read'

### If your app is in test mode

If your app is in test mode, you will need to add test users to your project.

Add your test users to the "Users and groups" section.

## Background Jobs for Calendar & Messaging

After configuring Gmail, Google Calendar, or Microsoft 365 integrations, you need to start the background jobs that sync data.

Register the following recurring jobs in your worker container:
```bash
# from your worker container
yarn command:prod cron:messaging:messages-import
yarn command:prod cron:messaging:message-list-fetch
yarn command:prod cron:calendar:calendar-event-list-fetch
yarn command:prod cron:calendar:calendar-events-import
yarn command:prod cron:messaging:ongoing-stale
yarn command:prod cron:calendar:ongoing-stale
yarn command:prod cron:workflow:automated-cron-trigger
```

## Email Configuration

1. Go to **Settings → Admin Panel → Configuration Variables**
2. Find the **Email** section
3. Configure your SMTP settings:

<ArticleTabs label1="Gmail" label2="Office365" label3="Smtp4dev">

  <ArticleTab>

  You will need to provision an [App Password](https://support.google.com/accounts/answer/185833).
  - EMAIL_DRIVER=smtp
  - EMAIL_SMTP_HOST=smtp.gmail.com
  - EMAIL_SMTP_PORT=465
  - EMAIL_SMTP_USER=gmail_email_address
  - EMAIL_SMTP_PASSWORD='gmail_app_password'

  </ArticleTab>

  <ArticleTab>

  Keep in mind that if you have 2FA enabled, you will need to provision an [App Password](https://support.microsoft.com/en-us/account-billing/manage-app-passwords-for-two-step-verification-d6dc8c6d-4bf7-4851-ad95-6d07799387e9).
  - EMAIL_DRIVER=smtp
  - EMAIL_SMTP_HOST=smtp.office365.com
  - EMAIL_SMTP_PORT=587
  - EMAIL_SMTP_USER=office365_email_address
  - EMAIL_SMTP_PASSWORD='office365_password'

  </ArticleTab>

  <ArticleTab>

  **smtp4dev** is a fake SMTP email server for development and testing.
  - Run the smtp4dev image: `docker run --rm -it -p 8090:80 -p 2525:25 rnwood/smtp4dev`
  - Access the smtp4dev ui here: [http://localhost:8090](http://localhost:8090)
  - Set the following variables:
    - EMAIL_DRIVER=smtp
    - EMAIL_SMTP_HOST=localhost
    - EMAIL_SMTP_PORT=2525

  </ArticleTab>

</ArticleTabs>

<ArticleWarning>
**Environment-only mode:** If you set `IS_CONFIG_VARIABLES_IN_DB_ENABLED=false`, add these variables to your `.env` file instead.
</ArticleWarning>