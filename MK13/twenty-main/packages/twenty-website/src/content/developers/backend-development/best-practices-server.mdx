---
title: Best Practices
icon: TbChecklist
image: /images/user-guide/tips/light-bulb.png
---

This document outlines the best practices you should follow when working on the backend.

## Follow a modular approach

The backend follows a modular approach, which is a fundamental principle when working with NestJS. Make sure you break down your code into reusable modules to maintain a clean and organized codebase. 
Each module should encapsulate a particular feature or functionality and have a well-defined scope. This modular approach enables clear separation of concerns and removes unnecessary complexities.  

## Expose services to use in modules

Always create services that have a clear and single responsibility, which enhances code readability and maintainability. Name the services descriptively and consistently. 

You should also expose services that you want to use in other modules. Exposing services to other modules is possible through NestJS's powerful dependency injection system, and promotes loose coupling between components. 

## Avoid using `any` type

When you declare a variable as `any`, TypeScript's type checker doesn't perform any type checking, making it possible to assign any type of values to the variable. TypeScript uses type inference to determine the type of variable based on the value. By declaring it as `any`, TypeScript can no longer infer the type. This makes it hard to catch type-related errors during development, leading to runtime errors and makes the code less maintainable, less reliable, and harder to understand for others.  

This is why everything should have a type. So if you create a new object with a first name and last name, you should create an interface or type that contains a first name and last name that defines the shape of the object you are manipulating.

<ArticleEditContent></ArticleEditContent>