---
title: Work with Figma
info: Learn how you can collaborate with Twenty's Figma
icon: TbBrandF<PERSON>
image: /images/user-guide/objects/objects.png
---

Figma is a collaborative interface design tool that aids in bridging the communication barrier between designers and developers.
This guide explains how you can collaborate with Figma.

## Access

1. **Access the shared link:** You can access the project's Figma file [here](https://www.figma.com/file/xt8O9mFeLl46C5InWwoMrN/Twenty).
2. **Sign in:** If you're not already signed in, Figma will prompt you to do so. 
Key features are only available to logged-in users, such as the developer mode and the ability to select a dedicated frame.

<ArticleWarning>

You will not be able to collaborate effectively without an account.

</ArticleWarning>


## Figma structure

On the left sidebar, you can access the different pages of Twenty's Figma. This is how they're organized:

- **Components page:** This is the first page. The designer uses it to create and organize the reusable design elements used throughout the design file. For example, buttons, icons, symbols, or any other reusable components. It serves to maintain consistency across the design.
- **Main page:** The second page is the main page, which shows the complete user interface of the project. You can press ***Play*** to use the full app prototype.
- **Features pages:** The other pages are typically dedicated to features in progress. They contain the design of specific features or modules of the application or website. They are typically still in progress.

## Useful Tips

With read-only access, you can't edit the design, but you can access all features that will be useful to convert the designs into code.

### Use the Dev mode

Figma's Dev Mode enhances developers' productivity by providing easy design navigation, effective asset management, efficient communication tools, toolbox integrations, quick code snippets, and key layer information, bridging the gap between design and development. You can learn more about Dev Mode [here](https://www.figma.com/dev-mode/).

Switch to the "Developer" mode in the right part of the toolbar to see design specs, copy CSS, and access assets. 

### Use the Prototype

Click on any element on the canvas and press the “Play” button at the top right edge of the interface to access the prototype view. Prototype mode allows you to interact with the design as if it were the final product. It demonstrates the flow between screens and how interface elements like buttons, links, or menus behave when interacted with.

1. **Understanding transitions and animations:** In the Prototype mode, you can view any transitions or animations added by a designer between screens or UI elements, providing clear visual instructions to developers on the intended behavior and style.
2. **Implementation clarification:** A prototype can also help reduce ambiguities. Developers can interact with it to gain a better understanding of the functionality or appearance of particular elements.

For more comprehensive details and guidance on learning the Figma platform, you can visit the official [Figma Documentation](https://help.figma.com/hc/en-us).

### Measure distances

Select an element, hold `Option` key (Mac) or `Alt` key (Windows), then hover over another element to see the distance between them.

### Figma extension for VSCode (Recommended)

[Figma for VS Code](https://marketplace.visualstudio.com/items?itemName=figma.figma-vscode-extension)
lets you navigate and inspect design files, collaborate with designers, track changes, and speed up implementation - all without leaving your text editor.
It's part of our recommended extensions.

## Collaboration

1. **Using Comments:** You are welcome to use the comment feature by clicking on the bubble icon in the left part of the toolbar.
2. **Cursor chat:** A nice feature of Figma is the Cursor chat. Just press `;` on Mac and `/` on Windows to send a message if you see someone else using Figma as the same time as you.

<ArticleEditContent></ArticleEditContent>