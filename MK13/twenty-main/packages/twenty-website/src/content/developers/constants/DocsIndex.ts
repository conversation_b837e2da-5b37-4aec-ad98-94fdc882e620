export const DOCS_INDEX = {
  'Getting started': {
    'Local Setup': [{ fileName: 'local-setup' }],
    'Self-Hosting': [
      { fileName: 'self-hosting' },
      { fileName: 'docker-compose' },
      { fileName: 'upgrade-guide' },
      { fileName: 'setup' },
      { fileName: 'cloud-providers' },
      { fileName: 'troubleshooting' },
    ],
    'API and Webhooks': [{ fileName: 'api' }, { fileName: 'webhooks' }],
  },
  Contributing: {
    'Bugs and Requests': [{ fileName: 'bug-and-requests' }],
    'Frontend Development': [
      { fileName: 'storybook' },
      { fileName: 'components' },
      { fileName: 'frontend-development' },
      { fileName: 'frontend-commands' },
      { fileName: 'work-with-figma' },
      { fileName: 'best-practices-front' },
      { fileName: 'style-guide' },
      { fileName: 'folder-architecture-front' },
      { fileName: 'hotkeys' },
    ],
    'Backend Development': [
      { fileName: 'backend-development' },
      { fileName: 'server-commands' },
      { fileName: 'feature-flags' },
      { fileName: 'folder-architecture-server' },
      { fileName: 'zapier' },
      { fileName: 'best-practices-server' },
      { fileName: 'custom-objects' },
      { fileName: 'queue' },
    ],
  },
  'User Guide': {
    'Empty Section': [],
  },
};
