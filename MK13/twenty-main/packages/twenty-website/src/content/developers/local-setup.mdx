---
title: Local Setup
icon: TbDeviceDesktop
image: /images/user-guide/fields/field.png
info: The guide for contributors (or curious developers) who want to run Twenty locally (on laptop, PC...)
---

## Prerequisites

<ArticleTabs label1="Linux and MacOS" label2="Windows (WSL)">
<ArticleTab>

Before you can install and use Twenty, make sure you install the following on your computer:
- [Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git)
- [Node v22](https://nodejs.org/en/download) 
- [yarn v4](https://yarnpkg.com/getting-started/install)
- [nvm](https://github.com/nvm-sh/nvm/blob/master/README.md)

<ArticleWarning>
`npm` won't work, you should use `yarn` instead. Yarn is now shipped with Node.js, so you don't need to install it separately.
You only have to run `corepack enable` to enable Yarn if you haven't done it yet.
</ArticleWarning>

</ArticleTab>

<ArticleTab>

1. Install WSL
Open PowerShell as Administrator and run: 
```powershell
wsl --install
```
You should now see a prompt to restart your computer. If not, restart it manually.

Upon restart, a powershell window will open and install Ubuntu. This may take up some time.
You'll see a prompt to create a username and password for your Ubuntu installation.

2. Install and configure git

```bash
sudo apt-get install git

git config --global user.name "Your Name"

git config --global user.email "<EMAIL>"
```

3. Install nvm, node.js and yarn
<ArticleWarning>

Use `nvm` to install the correct `node` version. The `.nvmrc` ensures all contributors use the same version.

</ArticleWarning>

```bash
sudo apt-get install curl

curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/master/install.sh | bash
```
Close and reopen your terminal to use nvm. Then run the following commands.

```bash

nvm install # installs recommended node version

nvm use # use recommended node version

corepack enable
```

</ArticleTab>
</ArticleTabs>

---

## Step 1: Git Clone

In your terminal, run the following command. 


<ArticleTabs label1="SSH (Recommended)" label2="HTTPS">
<ArticleTab>
If you haven't already set up SSH keys, you can learn how to do so [here](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/about-ssh). 
```bash
<NAME_EMAIL>:twentyhq/twenty.git
```
</ArticleTab>
<ArticleTab>

```bash
git clone https://github.com/twentyhq/twenty.git
```

</ArticleTab>
</ArticleTabs>

## Step 2: Position yourself at the root

```bash
cd twenty
```

You should run all commands in the following steps from the root of the project.

## Step 3: Set up a PostgreSQL Database

<ArticleTabs label1="Linux" label2="Mac OS" label3="Windows (WSL)">
  <ArticleTab>
    <b>Option 1 (preferred):</b> To provision your database locally:
    Use the following link to install Postgresql on your Linux machine: [Postgresql Installation](https://www.postgresql.org/download/linux/)
    ```bash
      psql postgres -c "CREATE DATABASE \"default\";" -c "CREATE DATABASE test;"
    ```
    Note: You might need to add `sudo -u postgres` to the command before `psql` to avoid permission errors.

    <b>Option 2:</b> If you have docker installed:
    ```bash
      make postgres-on-docker
    ```
  </ArticleTab>
  <ArticleTab>
    <b>Option 1 (preferred):</b> To provision your database locally with `brew`:

    ```bash
      brew install postgresql@16
      export PATH="/opt/homebrew/opt/postgresql@16/bin:$PATH"
      psql postgres -c "CREATE DATABASE \"default\";" -c "CREATE DATABASE test;"
    ```

    <b>Option 2:</b> If you have docker installed:
    ```bash
      make postgres-on-docker
    ```
  </ArticleTab>
  <ArticleTab>
    All the following steps are to be run in the WSL terminal (within your virtual machine)

    <b>Option 1:</b> To provision your Postgresql locally:
    Use the following link to install Postgresql on your Linux virtual machine: [Postgresql Installation](https://www.postgresql.org/download/linux/)
    ```bash
      psql postgres -c "CREATE DATABASE \"default\";" -c "CREATE DATABASE test;"
    ```
    Note: You might need to add `sudo -u postgres` to the command before `psql` to avoid permission errors.

    <b>Option 2:</b> If you have docker installed:  
    Running Docker on WSL adds an extra layer of complexity.
    Only use this option if you are comfortable with the extra steps involved, including turning on [Docker Desktop WSL2](https://docs.docker.com/desktop/wsl).
    ```bash
      make postgres-on-docker
    ```
  </ArticleTab>
</ArticleTabs>

You can now access the database at [localhost:5432](localhost:5432), with user `postgres` and password `postgres` .

## Step 4: Set up a Redis Database (cache)
Twenty requires a redis cache to provide the best performance

<ArticleTabs label1="Linux" label2="Mac OS" label3="Windows (WSL)">
  <ArticleTab>
    <b>Option 1:</b> To provision your Redis locally:
    Use the following link to install Redis on your Linux machine: [Redis Installation](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-linux/)

    <b>Option 2:</b> If you have docker installed:
    ```bash
      make redis-on-docker
    ```
  </ArticleTab>
  <ArticleTab>
    <b>Option 1 (preferred):</b> To provision your Redis locally with `brew`:
    ```bash
    brew install redis
    ```
    Start your redis server:
    ```brew services start redis```

    <b>Option 2:</b> If you have docker installed:
    ```bash
      make redis-on-docker
    ```
  </ArticleTab>
  <ArticleTab>
    <b>Option 1:</b> To provision your Redis locally:
    Use the following link to install Redis on your Linux virtual machine: [Redis Installation](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-linux/)

    <b>Option 2:</b> If you have docker installed:
    ```bash
      make redis-on-docker
    ```
  </ArticleTab>
</ArticleTabs>

If you need a Client GUI, we recommend [redis insight](https://redis.io/insight/) (free version available)

## Step 5: Setup environment variables

Use environment variables or `.env` files to configure your project. More info [here](https://twenty.com/developers/section/self-hosting/setup)

Copy the `.env.example` files in `/front` and `/server`:
```bash
cp ./packages/twenty-front/.env.example ./packages/twenty-front/.env
cp ./packages/twenty-server/.env.example ./packages/twenty-server/.env
```

## Step 6: Installing dependencies
To build Twenty server and seed some data into your database, run the following command:
```bash
yarn
```
Note that `npm` or `pnpm` won't work

## Step 7: Running the project

<ArticleTabs label1="Linux" label2="Mac OS" label3="Windows (WSL)">
  <ArticleTab>
    Depending on your Linux distribution, Redis server might be started automatically.
    If not, check the [Redis installation guide](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/) for your distro.
  </ArticleTab>
  <ArticleTab>
  Redis should already be running. If not, run:
    ```bash
    brew services start redis
    ```
  </ArticleTab>
  <ArticleTab>
    Depending on your Linux distribution, Redis server might be started automatically.
    If not, check the [Redis installation guide](https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/) for your distro.
  </ArticleTab>
</ArticleTabs>


Set up your database with the following command:
```bash
npx nx database:reset twenty-server
```

Start the server, the worker and the frontend services:
```bash
npx nx start twenty-server
npx nx worker twenty-server
npx nx start twenty-front
```

Alternatively, you can start all services at once:
```bash
npx nx start
```

## Step 8: Use Twenty

**Frontend**  

Twenty's frontend will be running at [http://localhost:3001](http://localhost:3001).  
You can log in using the default demo account: `<EMAIL>` (password: `<EMAIL>`)

**Backend**  

- Twenty's server will be up and running at [http://localhost:3000](http://localhost:3000) 
- The GraphQL API can be accessed at [http://localhost:3000/graphql](http://localhost:3000/graphql)
- The REST API can be reached at [http://localhost:3000/rest](http://localhost:3000/rest)



## Troubleshooting

If you encounter any problem, check [Troubleshooting](https://twenty.com/developers/section/self-hosting/troubleshooting) for solutions.

<ArticleEditContent />