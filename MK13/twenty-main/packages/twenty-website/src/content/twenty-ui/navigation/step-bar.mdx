---
title: Step Bar
icon: TbCircleCheckFilled
image: /images/user-guide/api/api.png
---

Displays progress through a sequence of numbered steps by highlighting the active step. It renders a container with steps, each represented by the `Step` component.

<ArticleTabs label1="Usage" label2="Props">
<ArticleTab>

<SandpackEditor content={`import { StepBar } from "@/ui/navigation/step-bar/components/StepBar";

export const MyComponent = () => {
  return (
    <StepBar activeStep={2}>
      <StepBar.Step>Step 1</StepBar.Step>
      <StepBar.Step>Step 2</StepBar.Step>
      <StepBar.Step>Step 3</StepBar.Step>
    </StepBar>
  );
};`} />

</ArticleTab>

<ArticleTab>

<ArticlePropsTable options={[
  ['activeStep', 'number', 'The index of the currently active step. This determines which step should be visually highlighted']
]} />


</ArticleTab>
</ArticleTabs>

<ArticleEditContent></ArticleEditContent>