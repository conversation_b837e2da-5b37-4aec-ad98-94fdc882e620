---
title: Color Scheme
icon: TbColorFilter
image: /images/user-guide/fields/field.png
---

## Color Scheme Card

Represents different color schemes and is specially tailored for light and dark themes. 

<ArticleTabs label1="Usage" label2="Props">
<ArticleTab>

<SandpackEditor content={`import { ColorSchemeCard } from "twenty-ui/display";

export const MyComponent = () => {
  return (
      <ColorSchemeCard
      variant="Dark"
      selected={true}
      />
  );
};`} />

</ArticleTab>

<ArticleTab>

<ArticlePropsTable options={[
  ['variant', 'string', 'The color scheme variant. Options include `Dark`, `Light`, and `System`', 'light'],
  ['selected', 'boolean', 'If `true`, displays a checkmark to indicate the selected color scheme', ''],
  ['additional props', '`React.ComponentPropsWithoutRef<\'div\'>`', 'Standard HTML `div` element props', '']
]} />

</ArticleTab>

</ArticleTabs>

## Color Scheme Picker 

Allows users to choose between different color schemes.

<ArticleTabs label1="Usage" label2="Props">
<ArticleTab>

<SandpackEditor content={`import { ColorSchemePicker } from "twenty-ui/display";

export const MyComponent = () => {
  return <ColorSchemePicker
  value="Dark"
  onChange
  />;
};`} />

</ArticleTab>

<ArticleTab>

<ArticlePropsTable options={[
  ['value', '`Color Scheme`', 'The currently selected color scheme'],
  ['onChange', 'function', 'The callback function you want to trigger when a user selects a color scheme']
]} />

</ArticleTab>

</ArticleTabs>

<ArticleEditContent></ArticleEditContent>