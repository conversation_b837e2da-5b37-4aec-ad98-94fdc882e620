---
title: Toggle
icon: TbToggleRight
image: /images/user-guide/table-views/table.png
---

<ArticleTabs label1="Usage" label2="Props">
<ArticleTab>

<SandpackEditor content={`import { Toggle } from "twenty-ui/input";

export const MyComponent = () => {
  return (
    <Toggle
    value = {true}
    onChange = {()=>console.log('On Change event')}
    color="green"
    toggleSize = "medium"
    />
  );
};`} />

</ArticleTab>
<ArticleTab>

<ArticlePropsTable options={[
  ['value', 'boolean', 'The current state of the toggle', '`false`'],
  ['onChange', 'function', 'Callback function triggered when the toggle state changes', ''],
  ['color', 'string', 'Color of the toggle when it\'s in the "on" state. If not provided, it uses the theme\'s blue color', ''],
  ['toggleSize', 'string', 'Size of the toggle, affecting both height and weight. Has two options: `small` and `medium`', 'medium']
]} />

</ArticleTab>
</ArticleTabs>

<ArticleEditContent></ArticleEditContent>