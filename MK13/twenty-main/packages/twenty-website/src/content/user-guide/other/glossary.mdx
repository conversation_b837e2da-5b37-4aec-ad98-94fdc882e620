---
title: Glossary
info: Get familiar with essential terminology used in Twenty.
icon: IconVocabulary
image: /images/user-guide/glossary/glossary.png
sectionInfo: Discover tips and tricks to optimize your experience.
---

## Company & People
The CRM has two fundamental types of records:
- A `Company` represents a business or organization.
- `People` represent your company's current and prospective customers or clients.

## Kanban
A `Kanban` is a way to track a business process. Pipelines are present within a *module* and have *stages*:
- A **module** has the logic for a certain business process (for example: sales, recruiting).
- **Stages** map the steps in your process (for example: new, ongoing, won, lost).

## Views 
You can customize the display of your records using views, setting different filters and sorting options for each view.

## Workspace
A `Workspace` typically represents a company using Twenty. It holds all the records and data that you and your team members add to Twenty.   
It has a single domain name, which is typically the domain name your company uses for employee email addresses.

## Field 
A field refers to a specific area where particular data is stored for an entity.

## Record 
A Record indicates an instance of an object, like a specific account or contact.

## Tasks 
Tasks in Twenty CRM are assigned activities relating to contacts, accounts, or opportunities.

## Opportunities 
Opportunities in Twenty CRM are potential deals or sales with accounts or contacts.

## Integration 
Integration are built-in tools that allow to link Twenty with other software or systems.

## User Profile
A User Profile is the information and settings specific to a workspace member in Twenty.

<ArticleEditContent></ArticleEditContent>
