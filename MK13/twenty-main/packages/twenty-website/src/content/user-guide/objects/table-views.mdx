---
title: Table Views
info: Learn how to customize and navigate Table Views.
icon: IconChecklist
image: /images/user-guide/table-views/table.png
sectionInfo: Discover how to use standard and custom objects in your workspace.
---

## About Table Views

Table views are visual representations of data structured in rows and columns.

## Create record

Add records as needed, without limits. To add a record, you can either click on the **+** button at the top right of the screen or at the top of the record **Name** column. 

Enter the record name then press `Enter` to save. To edit a record name, click on its name on its detail page.

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927071691?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>


## Delete record

**Index View:** To delete a record, select the checkbox next to the record and click the delete button below.

**Record Page:** Tap the **3 dots menu** in the top right corner, then select delete.


<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927073570?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

## Add a Custom Field

To create a custom field, click the **+** button at the right end of the table columns and select **Customize fields**. 

<img src="/images/user-guide/fields/quick-new-field.png" style={{width:'100%'}}/>

You can also do it by navigating to **Settings** > **Data Model** > **People**. Click on **Add Field**. Choose a field name and type. The new field will be available in the app.

<ArticleEditContent></ArticleEditContent>