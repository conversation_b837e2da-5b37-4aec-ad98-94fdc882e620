---
title: Standard and Custom Objects
info: Discover how to use standard and custom objects in your workspace.
icon: IconChecklist
image: /images/user-guide/objects/objects.png
sectionInfo: Discover how to use standard and custom objects in your workspace.
---

## Standard Objects

Standard objects are predefined entities in your workspace that offer integrated, standardized intelligence requiring no extra configuration. They're part of a shared data model accessible to all users of Twenty.

<img src="/images/user-guide/objects/standard-objects.png"style={{width:'100%'}}/>

### People

The `People` object aggregates customer relations data. It includes contact details and interaction history, providing a comprehensive view of your business's customer interactions.

### Company

The `Companies` object consolidates business account information. It encompasses all pertinent data such as industry, size, location, and contact personnel, thereby offering an integrated perspective of your business's organizational connections. It is both link to the People and Opportunities objects.

### Opportunities

The `Opportunities` object encapsulates deal-related data. It tracks the progression of potential sales, from prospecting to closure, recording stages, deal sizes, associated accounts, and expected closure dates. This provides a well-rounded view of your business's sales pipeline. 

## Custom objects

Custom objects are objects that you can create to store information that's unique to your organization. They're not built-in; members of your workspace can create and customize custom objects to hold information that standard objects aren't suitable for. For example, if you're SpaceX, you may want to create a custom object for Rockets and Launches.

<img src="/images/user-guide/objects/custom-objects.png"style={{width:'100%'}}/>

### Creating a new custom object 

To create a new custom object:

1. Go to Settings in the sidebar on the left.
2. Under Workspace, go to Data model. Here you'll be able to see an overview of all your existing Standard and Custom objects (both active and disabled).


<div style={{padding:'71.24% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/*********?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

3. Click on `+ New object` at the top, then choose Custom as the object type. Enter the name (both singular and plural), choose an icon, and add a description for your custom object and hit Save (at the top right). Using Listing as an example of custom object, the singular would be "listing" and the plural would be "listings" along with a description like "Listings that hosts created to showcase their property."

<div style={{padding:'71.24% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/926293493?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

4. Once you create your custom object, you'll be able to manage it. You can edit the name, icon and description, view the different fields, and add more fields.

<img src="/images/user-guide/objects/customize-fields.png"style={{width:'100%'}}/>

<ArticleEditContent></ArticleEditContent>