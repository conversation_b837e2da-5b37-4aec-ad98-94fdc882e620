---
title: Views, Sort and Filter
info: Find out how to create, manage and delete Object Views.
icon: IconChecklist
image: /images/user-guide/views/filter.png
sectionInfo: Discover how to use standard and custom objects in your workspace.
---
## About Views

You can see your records in different ways by creating views. In a view, you can apply filters and sorts to organize your content efficiently. For instance, on the `People` object, you can create a view to isolate US contacts by filtering those with a US phone number.

### Default View

Each object comes with an unfiltered, unsorted, and undeletable view known as the Default view. It's named after the object's plural name, such as "All Companies," "All People," "All Opportunities".

<img src="/images/user-guide/views/default-view.png" style={{width:'100%'}}/>

## Creating, Editing and Deleting Views

You can create several custom views and share them with your team.

### Creating a View

There are two ways to create a new view. Either directly from the `View Switcher`, or by filtering and sorting an existing view.

#### From View Switcher

1. Open the View Switcher
2. Click the `Add View` button at the bottom of the view switcher menu.
3. Choose an Icon and name for your View.
4. Choose a "View type" between Table and Kanban. 
5. (Kanban Views only) For your Kanban view, select the Select field you wish to use as the column header. The system prompts you to create one from the Settings before enabling Kanban view creation, if your object lacks a `Select field`.
6. Click "Create" to generate your new view.

The newly created view opens automatically.

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927639721?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

#### From Sorting and Filtering

When you change the `Sorting` and `Filtering` of an existing view, a `Save as new view` button will appear at the right edge of the `View Bar`. This will open the New View menu mentioned above, allowing you to create a new view out of an existing one.

<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927643495?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

### Editing and Deleting a View

To Edit or Delete a view:
1. Open the View Switcher
2. Hover the View you wish to edit
3. Click the `Pen Icon Button` that appears on the right

You will then be able to modify the view. To delete it, press the `Delete` button that just appeared.


<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927645774?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

## Navigating Between Views

To switch between views, simply open the view switcher and click the view you wish to open.

## Customizing Views

Each view can be customized to streamline your business processes. Views help segment your data. You can customize views using filters, sorts, and field display.

### Filtering a View

To filter a view:

-  Click **Filter** > select a field (for example: Name, Company in the People Object).
-  Choose a condition, select a value, and apply the filter.
-  Add more filters with `+ Add filter` or remove them with **X**.


<div style={{padding:'71.24% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/*********?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>

### Sorting a View

Order your fields data in ascending or descending order:
- Select **Sort**, choose a field, and define the sort order you desire.
- You can apply and arrange several sorts as needed.
<div style={{padding:'69.01% 0 0 0', position:'relative', margin: '32px 0px 0px'}}>
  <iframe 
    src="https://player.vimeo.com/video/927885588?autoplay=1&loop=1&autopause=0&background=1&amp;app_id=58479" 
    frameBorder="0" 
    allow="autoplay; fullscreen; picture-in-picture; clipboard-write" 
    style={{
      position:'absolute',
      top:0,
      left:0,
      width:'100%',
      height:'100%',
      borderRadius: '16px', 
      border:'2px solid black'
    }}
    title="Export data"
  ></iframe>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>


### Field display

You can customize which fields to display within a view. To hide a field, click **Options** > **Fields** and select the `-` button next to the field or click on the field column header and select **hide**.

You can rearrange fields by clicking their field column header and then press **Move Right** or **Move Left**.

### Opening Record

To open a record, click on the name in the first column. This action will open the corresponding Record page.

<ArticleEditContent></ArticleEditContent>
