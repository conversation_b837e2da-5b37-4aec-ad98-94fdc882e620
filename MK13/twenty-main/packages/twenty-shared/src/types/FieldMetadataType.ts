export enum FieldMetadataType {
  UUID = 'UUID',
  TEXT = 'TEXT',
  PHONES = 'PHONES',
  EMAILS = 'EMAILS',
  DATE_TIME = 'DATE_TIME',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  NUMBER = 'NUMBER',
  NUMERIC = 'NUMERIC',
  LINKS = 'LINKS',
  CURRENCY = 'CURRENCY',
  FULL_NAME = 'FULL_NAME',
  RATING = 'RATING',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  RELATION = 'RELATION',
  POSITION = 'POSITION',
  ADDRESS = 'ADDRESS',
  RAW_JSON = 'RAW_JSON',
  RICH_TEXT = 'RICH_TEXT',
  RICH_TEXT_V2 = 'RICH_TEXT_V2',
  ACTOR = 'ACTOR',
  ARRAY = 'ARRAY',
  TS_VECTOR = 'TS_VECTOR',
}
