{"name": "twenty-ui", "main": "dist/index.cjs", "module": "dist/index.mjs", "style": "./dist/style.css", "type": "module", "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@prettier/sync": "^0.5.2", "@swc/plugin-emotion": "4.0.2", "@types/babel__preset-env": "^7", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.15", "@wyw-in-js/babel-preset": "^0.6.0", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-inline-react-svg": "^2.0.2", "babel-plugin-module-resolver": "^5.0.2", "cpx": "^1.5.0", "tsx": "^4.19.3", "vite-plugin-svgr": "^4.3.0"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@linaria/react": "^6.2.1", "@monaco-editor/react": "^4.6.0", "@sniptt/guards": "^0.2.0", "@tabler/icons-react": "^3.31.0", "date-fns": "^2.30.0", "framer-motion": "^11.18.0", "glob": "^11.0.1", "hex-rgb": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.4.4", "react-tooltip": "^5.13.1", "recoil": "^0.7.7", "twenty-shared": "workspace:*", "zod": "3.23.8"}, "scripts": {"build": "npx vite build"}, "files": ["dist", "assets", "accessibility", "components", "display", "feedback", "input", "json-visualizer", "layout", "navigation", "testing", "theme", "utilities"], "sideEffects": ["**/*.css"], "exports": {"./style.css": "./dist/style.css", "./accessibility": {"types": "./dist/accessibility/index.d.ts", "import": "./dist/accessibility.mjs", "require": "./dist/accessibility.cjs"}, "./components": {"types": "./dist/components/index.d.ts", "import": "./dist/components.mjs", "require": "./dist/components.cjs"}, "./display": {"types": "./dist/display/index.d.ts", "import": "./dist/display.mjs", "require": "./dist/display.cjs"}, "./feedback": {"types": "./dist/feedback/index.d.ts", "import": "./dist/feedback.mjs", "require": "./dist/feedback.cjs"}, "./input": {"types": "./dist/input/index.d.ts", "import": "./dist/input.mjs", "require": "./dist/input.cjs"}, "./json-visualizer": {"types": "./dist/json-visualizer/index.d.ts", "import": "./dist/json-visualizer.mjs", "require": "./dist/json-visualizer.cjs"}, "./layout": {"types": "./dist/layout/index.d.ts", "import": "./dist/layout.mjs", "require": "./dist/layout.cjs"}, "./navigation": {"types": "./dist/navigation/index.d.ts", "import": "./dist/navigation.mjs", "require": "./dist/navigation.cjs"}, "./testing": {"types": "./dist/testing/index.d.ts", "import": "./dist/testing.mjs", "require": "./dist/testing.cjs"}, "./theme": {"types": "./dist/theme/index.d.ts", "import": "./dist/theme.mjs", "require": "./dist/theme.cjs"}, "./utilities": {"types": "./dist/utilities/index.d.ts", "import": "./dist/utilities.mjs", "require": "./dist/utilities.cjs"}}}