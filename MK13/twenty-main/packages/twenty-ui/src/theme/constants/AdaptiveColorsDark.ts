import { THEME_COMMON } from '@ui/theme/constants/ThemeCommon';

export const ADAPTIVE_COLORS_DARK = {
  green1: THEME_COMMON.color.green80,
  green2: THEME_COMMON.color.green70,
  green3: THEME_COMMON.color.green60,
  green4: THEME_COMMON.color.green50,
  turquoise1: THEME_COMMON.color.turquoise80,
  turquoise2: THEME_COMMON.color.turquoise70,
  turquoise3: THEME_COMMON.color.turquoise60,
  turquoise4: THEME_COMMON.color.turquoise50,
  sky1: THEME_COMMON.color.sky80,
  sky2: THEME_COMMON.color.sky70,
  sky3: THEME_COMMON.color.sky60,
  sky4: THEME_COMMON.color.sky50,
  blue1: THEME_COMMON.color.blue80,
  blue2: THEME_COMMON.color.blue70,
  blue3: THEME_COMMON.color.blue60,
  blue4: THEME_COMMON.color.blue50,
  red1: THEME_COMMON.color.red80,
  red2: THEME_COMMON.color.red70,
  red3: THEME_COMMON.color.red60,
  red4: THEME_COMMON.color.red50,
  orange1: THEME_COMMON.color.orange80,
  orange2: THEME_COMMON.color.orange70,
  orange3: THEME_COMMON.color.orange60,
  orange4: THEME_COMMON.color.orange50,
  yellow1: THEME_COMMON.color.yellow80,
  yellow2: THEME_COMMON.color.yellow70,
  yellow3: THEME_COMMON.color.yellow60,
  yellow4: THEME_COMMON.color.yellow50,
  pink1: THEME_COMMON.color.pink80,
  pink2: THEME_COMMON.color.pink70,
  pink3: THEME_COMMON.color.pink60,
  pink4: THEME_COMMON.color.pink50,
  purple1: THEME_COMMON.color.purple80,
  purple2: THEME_COMMON.color.purple70,
  purple3: THEME_COMMON.color.purple60,
  purple4: THEME_COMMON.color.purple50,
  gray1: THEME_COMMON.color.gray80,
  gray2: THEME_COMMON.color.gray70,
  gray3: THEME_COMMON.color.gray60,
  gray4: THEME_COMMON.color.gray50,
};
