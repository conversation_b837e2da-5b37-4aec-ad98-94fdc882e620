import { THEME_COMMON } from '@ui/theme/constants/ThemeCommon';

export const ADAPTIVE_COLORS_LIGHT = {
  green1: THEME_COMMON.color.green10,
  green2: THEME_COMMON.color.green20,
  green3: THEME_COMMON.color.green30,
  green4: THEME_COMMON.color.green40,
  turquoise1: THEME_COMMON.color.turquoise10,
  turquoise2: THEME_COMMON.color.turquoise20,
  turquoise3: THEME_COMMON.color.turquoise30,
  turquoise4: THEME_COMMON.color.turquoise40,
  sky1: THEME_COMMON.color.sky10,
  sky2: THEME_COMMON.color.sky20,
  sky3: THEME_COMMON.color.sky30,
  sky4: THEME_COMMON.color.sky40,
  blue1: THEME_COMMON.color.blue10,
  blue2: THEME_COMMON.color.blue20,
  blue3: THEME_COMMON.color.blue30,
  blue4: THEME_COMMON.color.blue40,
  red1: THEME_COMMON.color.red10,
  red2: THEME_COMMON.color.red20,
  red3: THEME_COMMON.color.red30,
  red4: THEME_COMMON.color.red40,
  orange1: THEME_COMMON.color.orange10,
  orange2: THEME_COMMON.color.orange20,
  orange3: THEME_COMMON.color.orange30,
  orange4: THEME_COMMON.color.orange40,
  yellow1: THEME_COMMON.color.yellow10,
  yellow2: THEME_COMMON.color.yellow20,
  yellow3: THEME_COMMON.color.yellow30,
  yellow4: THEME_COMMON.color.yellow40,
  pink1: THEME_COMMON.color.pink10,
  pink2: THEME_COMMON.color.pink20,
  pink3: THEME_COMMON.color.pink30,
  pink4: THEME_COMMON.color.pink40,
  purple1: THEME_COMMON.color.purple10,
  purple2: THEME_COMMON.color.purple20,
  purple3: THEME_COMMON.color.purple30,
  purple4: THEME_COMMON.color.purple40,
  gray1: THEME_COMMON.color.gray10,
  gray2: THEME_COMMON.color.gray20,
  gray3: THEME_COMMON.color.gray30,
  gray4: THEME_COMMON.color.gray40,
};
