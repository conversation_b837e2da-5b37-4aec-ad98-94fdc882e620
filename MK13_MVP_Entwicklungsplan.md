# MK13 MVP Entwicklungsplan

Dieser Plan beschreibt die Schritte zur Entwicklung des Minimum Viable Product (MVP) des MK13 Personal AI Assistant Systems, basierend auf den bereitgestellten Anweisungen und den getroffenen Entscheidungen.

**Zusammenfassung der Entscheidungen:**

*   **Backend:** Ein neuer FastAPI-Dienst wird erstellt, der mit dem vorhandenen NestJS-Backend von `twenty-main` kommuniziert.
*   **KI-Integration:** Bestimmte Module oder Konzepte aus `agent-zero-main` werden genutzt, der Rest der KI-Logik wird im FastAPI-Dienst neu implementiert.
*   **Kontextmanagement & Datenmodelle:** Konzepte aus `platform-develop` dienen als Inspiration, werden aber neu in FastAPI implementiert.
*   **Frontend:** Ein komplett neues Electron-React-Projekt wird aufgesetzt, `twenty-main/packages/twenty-front` dient nur als Referenz.

---

## Detaillierter Plan für das MK13 MVP

Der Plan ist in mehrere Phasen unterteilt, um eine strukturierte Entwicklung zu gewährleisten.

### Phase 1: Projektstruktur und Basis-Setup

1.  **Verzeichnisstruktur erstellen:**
    *   Erstelle ein neues Hauptverzeichnis für das MK13 MVP, z.B. `mk13-mvp/`.
    *   Innerhalb von `mk13-mvp/` erstelle Unterverzeichnisse für `backend/` (FastAPI) und `frontend/` (Electron-React).

2.  **Backend (FastAPI) Initialisierung:**
    *   Initialisiere ein neues Python-Projekt im `backend/` Verzeichnis.
    *   Installiere die Kernabhängigkeiten: `fastapi`, `uvicorn`, `supabase`, `celery`, `redis`, `openai`, `fastapi-socketio`.
    *   Erstelle die `main.py` mit der grundlegenden FastAPI-Konfiguration.
    *   Erstelle die `.env`-Datei mit den erforderlichen Umgebungsvariablen (`SUPABASE_URL`, `SUPABASE_KEY`, `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, `OPENAI_API_KEY`, `REDIS_URL`).

3.  **Frontend (Electron-React) Initialisierung:**
    *   Initialisiere ein neues Electron-React-Projekt im `frontend/` Verzeichnis mit Electron Forge.
    *   Installiere zusätzliche Abhängigkeiten: `socket.io-client`, `redux`, `@reduxjs/toolkit`, `axios`.

### Phase 2: Backend-Entwicklung (FastAPI)

1.  **Datenbankintegration (Supabase):**
    *   Implementiere den Supabase-Client im FastAPI-Backend.
    *   Definiere die Datenbankmodelle (Tabellen) für `users`, `contexts` und `feedback` gemäß den Anweisungen:
        *   `users`: `id`, `email`, `name`, `google_token`
        *   `contexts`: `id`, `user_id`, `name`, `state`, `metadata`
        *   `feedback`: `id`, `user_id`, `action_id`, `feedback_type`, `timestamp`
    *   Implementiere CRUD-Operationen für diese Tabellen.

2.  **API-Endpunkte definieren:**
    *   **KI-Interaktionen:** Endpunkte für Text- und Spracheingaben, die die KI (OpenAI GPT-4o-mini) nutzen. Hier werden Konzepte aus `agent-zero-main` (z.B. Prompt-Struktur, Agenten-Verhalten) als Inspiration dienen.
    *   **Kontextmanagement:** Endpunkte zum Speichern, Abrufen und Aktualisieren von Benutzerkontexten. Konzepte aus `platform-develop` (z.B. Struktur von Kontextdaten) werden als Inspiration dienen.
    *   **Vorschläge:** Endpunkte für proaktive E-Mail- und Meeting-Vorschläge.

3.  **Google Workspace-Integration:**
    *   Implementiere die OAuth2-Authentifizierung für Google Workspace.
    *   Entwickle Funktionen zur Interaktion mit Gmail (E-Mails lesen, senden), Google Kalender (Termine abrufen, erstellen) und Google Drive (Dokumente zugreifen). Diese Funktionen werden über den FastAPI-Dienst bereitgestellt.

4.  **Hintergrundaufgaben (Celery & Redis):**
    *   Konfiguriere Celery mit Redis als Broker.
    *   Implementiere Celery-Tasks für die E-Mail-Überwachung und andere asynchrone Aufgaben.

5.  **Echtzeitkommunikation (Socket.io):**
    *   Richte `fastapi-socketio` im Backend ein, um Echtzeitkommunikation mit dem Frontend zu ermöglichen (z.B. für KI-Vorschläge oder Benachrichtigungen).

### Phase 3: Frontend-Entwicklung (Electron-React)

1.  **Ghost UI Implementierung:**
    *   Entwickle die minimalistische Benutzeroberfläche, die nur bei Bedarf erscheint.
    *   Nutze React-Komponenten für Overlays, Benachrichtigungen und Eingabefelder.

2.  **Zustandsmanagement (Redux):**
    *   Konfiguriere Redux für das globale Zustandsmanagement von Benutzerdaten, Kontexten und KI-Vorschlägen.

3.  **Echtzeitkommunikation (Socket.io-Client):**
    *   Implementiere den `socket.io-client` im Frontend, um mit dem FastAPI-Backend zu kommunizieren.

4.  **Sprach- und Texteingabe:**
    *   Integriere die Web Speech API für die Spracherkennung.
    *   Implementiere ein Textfeld für Texteingaben.

5.  **Kontextmanagement (Client-seitig):**
    *   Implementiere Logik zur Erkennung des aktuellen Arbeitskontexts (Windows, macOS, Linux) und sende diese Informationen an das Backend.

### Phase 4: Sicherheit und Tests

1.  **Sicherheit:**
    *   Stelle sicher, dass OAuth2 für die Benutzerauthentifizierung korrekt implementiert ist.
    *   Implementiere AES-256-Verschlüsselung für sensible Daten (z.B. Google Tokens).
    *   Stelle sicher, dass alle Kommunikationen TLS 1.3 verwenden.

2.  **Tests:**
    *   Schreibe Unit- und Integrationstests für Backend (Pytest) und Frontend (Jest).
    *   Strebe eine hohe Testabdeckung für kritische Pfade an.

---

## Architektur-Diagramm

```mermaid
graph TD
    subgraph Benutzer
        A[Benutzer] --> B(Electron Frontend)
    end

    subgraph Electron Frontend
        B --> C{Ghost UI}
        B --> D[Sprach-/Texteingabe]
        B --> E[Redux State Management]
        B --> F[Socket.io Client]
        B --> G[Kontexterkennung (OS-spezifisch)]
    end

    subgraph FastAPI Backend
        H[FastAPI Server] --> I(API Endpunkte)
        I --> J[KI-Interaktionen]
        I --> K[Kontextmanagement]
        I --> L[Vorschläge]
        H --> M[Socket.io Server]
        H --> N[Google Workspace Integration]
        N --> O[Gmail API]
        N --> P[Calendar API]
        N --> Q[Drive API]
        H --> R[Celery Worker]
        R --> S[Redis Broker]
        H --> T[Supabase Client]
    end

    subgraph Externe Dienste
        U[OpenAI GPT-4o-mini]
        V[Supabase PostgreSQL]
        W[Google Cloud APIs]
        X[NestJS Backend (twenty-main)]
        Y[Agent Zero (Konzepte)]
        Z[Platform Develop (Konzepte)]
    end

    F --> M
    J --> U
    K --> V
    N --> W
    H <--> X
    J --> Y
    K --> Z
    L --> J
    L --> N
    G --> K
```

**Erläuterungen zum Diagramm:**

*   **Benutzer:** Interagiert mit dem Electron Frontend.
*   **Electron Frontend:**
    *   **Ghost UI:** Die minimalistische Benutzeroberfläche.
    *   **Sprach-/Texteingabe:** Erfasst Benutzereingaben.
    *   **Redux State Management:** Verwaltet den Anwendungszustand.
    *   **Socket.io Client:** Kommuniziert in Echtzeit mit dem FastAPI Backend.
    *   **Kontexterkennung (OS-spezifisch):** Erkennt den aktuellen Arbeitskontext des Benutzers auf dem Betriebssystem.
*   **FastAPI Backend:**
    *   **FastAPI Server:** Der Hauptserver.
    *   **API Endpunkte:** Bietet Schnittstellen für KI-Interaktionen, Kontextmanagement und Vorschläge.
    *   **KI-Interaktionen:** Nutzt OpenAI GPT-4o-mini und Konzepte aus Agent Zero.
    *   **Kontextmanagement:** Speichert und verwaltet Kontexte in Supabase.
    *   **Vorschläge:** Generiert proaktive Vorschläge basierend auf KI und Kontext.
    *   **Socket.io Server:** Ermöglicht Echtzeitkommunikation mit dem Frontend.
    *   **Google Workspace Integration:** Interagiert mit Gmail, Kalender und Drive APIs.
    *   **Celery Worker & Redis Broker:** Für asynchrone Hintergrundaufgaben wie E-Mail-Überwachung.
    *   **Supabase Client:** Für die Datenbankinteraktion.
*   **Externe Dienste:**
    *   **OpenAI GPT-4o-mini:** Das Sprachmodell für die KI.
    *   **Supabase PostgreSQL:** Die Datenbank.
    *   **Google Cloud APIs:** Die Schnittstellen zu Gmail, Kalender und Drive.
    *   **NestJS Backend (twenty-main):** Das bestehende Backend, mit dem der FastAPI-Dienst kommuniziert.
    *   **Agent Zero (Konzepte):** Konzepte und Module, die als Inspiration für die KI-Logik dienen.
    *   **Platform Develop (Konzepte):** Konzepte für Kontextmanagement und Datenmodelle, die als Inspiration dienen.