{"name": "frontend", "productName": "frontend", "version": "1.0.0", "description": "My Electron application description", "main": ".webpack/main", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx ."}, "keywords": [], "author": "vince", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-webpack": "^7.8.1", "@electron/fuses": "^1.8.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "css-loader": "^6.11.0", "electron": "36.4.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.31.0", "fork-ts-checker-webpack-plugin": "^7.3.0", "node-loader": "^2.1.0", "style-loader": "^3.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "~4.5.4"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "electron-squirrel-startup": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "redux": "^5.0.1", "socket.io-client": "^4.8.1"}}